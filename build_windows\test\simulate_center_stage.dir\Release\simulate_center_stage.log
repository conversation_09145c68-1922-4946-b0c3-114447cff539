﻿  simulate_center_stage.cpp
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(44,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(97,11): warning C4251: “czcv_camera::Any::m_ptr”: class“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(67): message : 参见“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(98,19): warning C4251: “czcv_camera::Any::m_tpIndex”: class“std::type_index”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\typeindex(25): message : 参见“std::type_index”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(117,14): warning C4251: “czcv_camera::DynamicParams::_mu”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(168,31): warning C4251: “czcv_camera::DynamicParams::_keyValues”: class“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(162): message : 参见“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_capi.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(39,21): warning C4251: “czcv_camera::AbstarctModel::_profileData”: class“czcv_camera::ProfileData”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h(9): message : 参见“czcv_camera::ProfileData”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(41,34): warning C4251: “czcv_camera::AbstarctModel::_modelConfig”: class“std::vector<cv::String,std::allocator<cv::String>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/persistence.hpp(417): message : 参见“std::vector<cv::String,std::allocator<cv::String>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(45,21): warning C4251: “czcv_camera::AbstarctModel::_modelName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(24,17): warning C4251: “czcv_camera::DetInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30,29): warning C4251: “czcv_camera::DetInputOutput::_bbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(31,49): warning C4251: “czcv_camera::DetInputOutput::_landmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32,49): warning C4251: “czcv_camera::DetInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(59,17): warning C4251: “czcv_camera::TrackerInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(60,29): warning C4251: “czcv_camera::TrackerInputOutput::_inBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(67,29): warning C4251: “czcv_camera::TrackerInputOutput::_trackedBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(68,49): warning C4251: “czcv_camera::TrackerInputOutput::_trackedLandmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(69,42): warning C4251: “czcv_camera::TrackerInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74,41): warning C4251: “czcv_camera::TrackerInputOutput::_gestureResults”: class“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74): message : 参见“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(261,45): warning C4251: “czcv_camera::BaseTracker::_detector”: class“std::shared_ptr<czcv_camera::BaseObjectDetector>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/detector_factory.h(65): message : 参见“std::shared_ptr<czcv_camera::BaseObjectDetector>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(268,19): warning C4251: “czcv_camera::BaseTracker::_lastRoi”: class“cv::Rect_<int>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/types.hpp(459): message : 参见“cv::Rect_<int>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(210,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(211,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(27): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(224,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(225,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81,49): warning C4251: “czcv_camera::PersonCenterStager::_impl”: class“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”需要有 dll 接口由 class“czcv_camera::PersonCenterStager”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81): message : 参见“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_api.h(36,21): warning C4251: “czcv_camera::Android_API::_modeldir”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Android_API”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_api.h(42,20): warning C4251: “czcv_camera::Android_API::_muMode”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Android_API”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(91,44): warning C4101: “e”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(119,44): warning C4101: “e”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(197,44): warning C4101: “e”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(205,44): warning C4101: “e”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(387,44): warning C4267: “+=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(388,54): warning C4267: “+=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(389,45): warning C4267: “+=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(390,51): warning C4267: “+=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(391,61): warning C4267: “+=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(413,18): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(488,27): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(495,111): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(495,90): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(495,74): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(495,59): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(496,108): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(496,87): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(497,116): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(497,101): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(498,112): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(498,96): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(505,111): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(505,90): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(505,74): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(505,59): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(509,162): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(509,119): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(509,95): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(509,55): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(516,27): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(529,111): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(529,90): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(529,74): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(529,59): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(530,108): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(530,87): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(531,116): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(531,101): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(532,112): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(532,96): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(539,111): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(539,90): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(539,74): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(539,59): warning C4244: “参数”: 从“float”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(543,162): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(543,119): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(543,95): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(543,55): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(547,26): warning C4244: “初始化”: 从“__int64”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(549,19): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\test\simulate_center_stage.cpp(555,26): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
  simulate_center_stage.vcxproj -> D:\Program\Project\project\czcv_camera_new\output\x86\bin\Release\simulate_center_stage.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
