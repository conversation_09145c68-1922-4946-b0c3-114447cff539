import cv2
import os

def extract_frames():
    # 输入视频路径和输出目录
    video_path = r"D:\20250919(04).mp4"
    output_dir = r"D:\Download\images"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Error: Cannot open video file!")
        return
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video FPS: {fps}")
    print(f"Total frames: {total_frames}")
    
    # 计算起始和结束帧
    start_frame = int(735 * fps)  # 第5秒
    end_frame = int(745 * fps)   # 第53秒
    
    print(f"Extracting frames from {start_frame} to {end_frame}")
    
    # 跳转到起始帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
    
    frame_count = 0
    
    for i in range(start_frame, end_frame + 1):
        ret, frame = cap.read()
        
        if not ret:
            print(f"End of video reached at frame {i}")
            break
        
        # 生成文件名
        filename = os.path.join(output_dir, f"frame_{frame_count:06d}.jpg")
        
        # 保存图像
        cv2.imwrite(filename, frame)
        
        frame_count += 1
        
        # 显示进度
        if frame_count % 100 == 0:
            print(f"Extracted {frame_count} frames...")
    
    print(f"Extraction completed! Total frames extracted: {frame_count}")
    
    cap.release()

if __name__ == "__main__":
    # import os
    # import cv2
    # srcdir = r"D:\Download\images"
    # for fn in os.listdir(srcdir):
    #     if fn.endswith('.jpg'):
    #         img = cv2.imread(os.path.join(srcdir, fn))
    #         h, w = img.shape[:2]
    #         offset = (w - h) // 2
    #         subimg = img[:, offset:offset+h]
    #         cv2.imwrite(os.path.join(srcdir, fn), subimg)
            


    extract_frames()