import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from collections import defaultdict
import pandas as pd

def parse_doa_file(filename):
    """
    解析DOA文件，返回按角度分组的数据
    """
    data = defaultdict(list)
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(', ')
                if len(parts) == 3:
                    angle = float(parts[0])  # 角度a
                    fitted_value = float(parts[1])  # 拟合值b
                    actual_value = float(parts[2])  # 实际值c
                    
                    data[angle].append({
                        'fitted': fitted_value,
                        'actual': actual_value
                    })
    
    return data

def uniform_sample(values, n_samples=20):
    """
    从值列表中均匀采样n_samples个值
    """
    if len(values) <= n_samples:
        return values
    
    # 计算采样间隔
    indices = np.linspace(0, len(values) - 1, n_samples, dtype=int)
    return [values[i] for i in indices]

def calculate_offset_for_angle(angle_data, n_samples=20):
    """
    计算单个角度的偏移量
    """
    if not angle_data:
        return None
    
    # 提取实际值c
    actual_values = [item['actual'] for item in angle_data]
    fitted_values = [item['fitted'] for item in angle_data]
    
    # 均匀采样20个实际值
    sampled_actual = uniform_sample(actual_values, n_samples)
    
    # 对应的拟合值（假设所有拟合值相同，取第一个）
    fitted_value = fitted_values[0]
    
    # 计算偏移量：实际值与拟合值的差值
    offsets = [actual - fitted_value for actual in sampled_actual]
    
    # 返回平均偏移量
    return np.mean(offsets)

def smooth_offsets(angles, offsets, window_length=5):
    """
    对偏移量进行平滑处理
    """
    if len(offsets) < window_length:
        window_length = len(offsets) if len(offsets) % 2 == 1 else len(offsets) - 1
    
    if window_length < 3:
        return offsets
    
    # 使用Savitzky-Golay滤波器进行平滑
    smoothed = signal.savgol_filter(offsets, window_length, 2)
    return smoothed

def calculate_doa_offsets(filename, n_samples=20, smooth_window=5):
    """
    主函数：计算DOA偏移量
    """
    print(f"正在解析文件: {filename}")
    
    # 解析文件
    data = parse_doa_file(filename)
    
    print(f"找到 {len(data)} 个不同的角度")
    
    # 计算每个角度的偏移量
    results = []
    
    for angle in sorted(data.keys()):
        offset = calculate_offset_for_angle(data[angle], n_samples)
        if offset is not None:
            results.append({
                'angle': angle,
                'offset': offset,
                'sample_count': len(data[angle])
            })
            print(f"角度 {angle}°: 偏移量 = {offset:.6f}, 样本数 = {len(data[angle])}")
    
    if not results:
        print("没有找到有效的数据")
        return None
    
    # 转换为DataFrame便于处理
    df = pd.DataFrame(results)
    
    # 对偏移量进行平滑处理
    if len(df) > 1:
        smoothed_offsets = smooth_offsets(df['angle'].values, df['offset'].values, smooth_window)
        df['smoothed_offset'] = smoothed_offsets
    else:
        df['smoothed_offset'] = df['offset']
    
    return df

def plot_results(df, output_path=None):
    """
    绘制结果图表
    """
    if df is None or df.empty:
        print("没有数据可以绘制")
        return

    plt.figure(figsize=(12, 8))

    # 转换为numpy数组以避免pandas兼容性问题
    angles = df['angle'].values
    offsets = df['offset'].values
    smoothed_offsets = df['smoothed_offset'].values
    sample_counts = df['sample_count'].values

    # 原始偏移量
    plt.subplot(2, 1, 1)
    plt.plot(angles, offsets, 'o-', label='原始偏移量', alpha=0.7)
    plt.plot(angles, smoothed_offsets, 's-', label='平滑后偏移量', linewidth=2)
    plt.xlabel('角度 (度)')
    plt.ylabel('偏移量')
    plt.title('DOA偏移量分析')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 样本数量分布
    plt.subplot(2, 1, 2)
    plt.bar(angles, sample_counts, alpha=0.7, color='orange')
    plt.xlabel('角度 (度)')
    plt.ylabel('样本数量')
    plt.title('各角度样本数量分布')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {output_path}")

    plt.show()

def save_results(df, output_path):
    """
    保存结果到CSV文件
    """
    if df is None or df.empty:
        print("没有数据可以保存")
        return
    
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"结果已保存到: {output_path}")

if __name__ == "__main__":
    # 配置参数
    input_file = "czcv_debug_doa.txt"
    n_samples = 20  # 每个角度采样的数量
    smooth_window = 5  # 平滑窗口大小
    
    # 计算偏移量
    results_df = calculate_doa_offsets(input_file, n_samples, smooth_window)
    
    if results_df is not None:
        # 显示统计信息
        print("\n=== 统计信息 ===")
        print(f"总角度数: {len(results_df)}")
        print(f"偏移量范围: {results_df['offset'].min():.6f} ~ {results_df['offset'].max():.6f}")
        print(f"平滑后偏移量范围: {results_df['smoothed_offset'].min():.6f} ~ {results_df['smoothed_offset'].max():.6f}")
        print(f"平均偏移量: {results_df['offset'].mean():.6f}")
        print(f"平滑后平均偏移量: {results_df['smoothed_offset'].mean():.6f}")
        
        # 保存结果
        save_results(results_df, "doa_offset_results.csv")
        
        # 绘制图表
        plot_results(results_df, "doa_offset_analysis.png")
        
        # 显示前几行结果
        print("\n=== 前10个角度的结果 ===")
        print(results_df.head(10).to_string(index=False))
    else:
        print("处理失败，请检查输入文件格式")
