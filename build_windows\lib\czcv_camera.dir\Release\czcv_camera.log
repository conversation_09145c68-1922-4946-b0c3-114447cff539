﻿  czcv_center_stage.cpp
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(44,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(97,11): warning C4251: “czcv_camera::Any::m_ptr”: class“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(67): message : 参见“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(98,19): warning C4251: “czcv_camera::Any::m_tpIndex”: class“std::type_index”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\typeindex(25): message : 参见“std::type_index”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(117,14): warning C4251: “czcv_camera::DynamicParams::_mu”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(168,31): warning C4251: “czcv_camera::DynamicParams::_keyValues”: class“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(162): message : 参见“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_capi.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(39,21): warning C4251: “czcv_camera::AbstarctModel::_profileData”: class“czcv_camera::ProfileData”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h(9): message : 参见“czcv_camera::ProfileData”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(41,34): warning C4251: “czcv_camera::AbstarctModel::_modelConfig”: class“std::vector<cv::String,std::allocator<cv::String>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/persistence.hpp(417): message : 参见“std::vector<cv::String,std::allocator<cv::String>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(45,21): warning C4251: “czcv_camera::AbstarctModel::_modelName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(24,17): warning C4251: “czcv_camera::DetInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30,29): warning C4251: “czcv_camera::DetInputOutput::_bbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(31,49): warning C4251: “czcv_camera::DetInputOutput::_landmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32,49): warning C4251: “czcv_camera::DetInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(59,17): warning C4251: “czcv_camera::TrackerInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(60,29): warning C4251: “czcv_camera::TrackerInputOutput::_inBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(67,29): warning C4251: “czcv_camera::TrackerInputOutput::_trackedBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(68,49): warning C4251: “czcv_camera::TrackerInputOutput::_trackedLandmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(69,42): warning C4251: “czcv_camera::TrackerInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74,41): warning C4251: “czcv_camera::TrackerInputOutput::_gestureResults”: class“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74): message : 参见“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(261,45): warning C4251: “czcv_camera::BaseTracker::_detector”: class“std::shared_ptr<czcv_camera::BaseObjectDetector>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/detector_factory.h(65): message : 参见“std::shared_ptr<czcv_camera::BaseObjectDetector>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(268,19): warning C4251: “czcv_camera::BaseTracker::_lastRoi”: class“cv::Rect_<int>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/types.hpp(459): message : 参见“cv::Rect_<int>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(210,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(211,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(27): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(224,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(225,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81,49): warning C4251: “czcv_camera::PersonCenterStager::_impl”: class“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”需要有 dll 接口由 class“czcv_camera::PersonCenterStager”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81): message : 参见“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”的声明
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\../utils/async_runner.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\config/config_setter.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\tracker/person_assert/rknn_person_assert.h(80,26): warning C4305: “初始化”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\detector/detect_white_board.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  cl_version.h: CL_TARGET_OPENCL_VERSION is not defined. Defaulting to 220 (OpenCL 2.2)
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(55,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(64,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(70,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(86,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(165,17): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(786,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2106,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2721,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3334,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(485,46): warning C4244: “参数”: 从“const double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(535,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(536,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(537,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(538,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(565,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(565,38): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(622,27): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(626,31): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(627,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(658,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(674,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(763,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(782,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(822,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(875,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(942,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(945,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(946,39): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(961,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(971,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1021,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1040,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1148,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1172,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1384,70): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1386,70): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1388,68): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1390,68): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1468,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1469,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1473,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1481,41): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1482,42): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1490,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1502,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1510,40): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1511,43): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1519,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1530,96): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1530,78): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1699,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1699,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1700,79): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1700,61): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1702,28): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1705,40): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1709,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1717,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1718,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1724,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1725,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1729,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1737,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1738,34): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1746,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1758,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1766,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1767,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1775,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1790,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1790,44): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1816,86): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2094,72): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2094,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2377,1): warning C4305: “参数”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2416,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2416,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2419,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2425,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2475,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2475,39): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2495,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2495,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2876,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2881,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2881,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2902,74): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2902,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2979,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2979,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2987,45): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2987,34): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3020,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3081,83): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3081,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3086,44): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3089,48): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3099,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3107,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3108,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3114,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3115,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3119,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3127,37): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3128,38): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3136,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3148,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3156,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3157,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3165,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3180,66): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3180,48): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3348,79): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3567,58): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3567,49): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4038,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4827,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5394,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5910,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6556,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4822,106): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4822,77): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4968,109): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4968,81): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5161,47): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5162,47): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5170,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5170,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5196,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5196,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5211,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5212,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5279,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5280,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5293,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5294,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5297,60): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5297,57): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5303,107): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5303,91): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5304,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5304,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5304,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5304,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5316,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5316,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5316,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5316,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5324,119): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5324,98): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5473,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5474,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5964,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5965,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6109,38): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6136,32): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6284,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6285,56): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6561,36): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6578,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6600,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6602,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6701,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(140,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(139): message : 在编译 类 模板 成员函数“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2665): message : 查看对正在编译的函数 模板 实例化“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(106): message : 查看对正在编译的 类 模板 实例化“czcv_camera::_Bbox<float>”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(141,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,84): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58): message : 在编译 类 模板 成员函数“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1148): message : 查看对正在编译的函数 模板 实例化“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,74): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,65): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,58): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
    正在创建库 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.lib 和对象 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.exp
  czcv_camera.vcxproj -> D:\Program\Project\project\czcv_camera_new\output\x86\bin\Release\czcv_camera.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
