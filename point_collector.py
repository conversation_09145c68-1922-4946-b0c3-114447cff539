#!/usr/bin/env python
"""
Point Collector - 使用鼠标点击收集图像上的点坐标
使用方法: python point_collector.py <directory_path>

功能特性:
- 自动读取目录下已有的 points_output.txt 文件
- 如果图片已有标注数据，会加载并显示已有的点
- 可以在已有标注基础上继续添加新的点
- 已有的点和新添加的点都会显示序号

操作说明:
- 左键点击图像上的点来收集坐标
- 按 SPACE 键进入下一张图片
- 按 ESC 键退出程序并保存结果
"""

import cv2 as cv
import numpy as np
import os
import sys
import glob
import argparse
import re

class PointCollector:
    def __init__(self):
        self.points = []
        self.current_image = None
        self.current_filename = ""
        self.all_results = {}
        
    def load_existing_points(self, directory_path):
        """读取已有的points_output.txt文件并解析点坐标数据"""
        output_file = os.path.join(directory_path, "points_output.txt")

        if not os.path.exists(output_file):
            print("未找到已有的points_output.txt文件，将创建新的标注")
            return

        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                print("points_output.txt文件为空")
                return

            # 使用正则表达式解析文件内容
            # 匹配格式: {"filename.jpg", {Point2f(x, y), Point2f(x, y), ...}}
            pattern = r'\{"([^"]+)",\s*\{([^}]*)\}\}'
            matches = re.findall(pattern, content)

            for filename, points_str in matches:
                # 解析点坐标
                point_pattern = r'Point2f\((\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?)\)'
                point_matches = re.findall(point_pattern, points_str)

                points = []
                for x_str, y_str in point_matches:
                    x = int(float(x_str))
                    y = int(float(y_str))
                    points.append((x, y))

                if points:
                    self.all_results[filename] = points
                    print(f"加载已有标注: {filename} - {len(points)} 个点")

            print(f"总共加载了 {len(self.all_results)} 个文件的已有标注")

        except Exception as e:
            print(f"读取points_output.txt文件时出错: {e}")
            print("将忽略已有文件，重新开始标注")

    def mouse_callback(self, event, x, y, flags, param):
        if event == cv.EVENT_LBUTTONDOWN:
            # 记录点击的坐标
            self.points.append((x, y))
            point_index = len(self.points)
            print(f"添加点 {point_index}: ({x}, {y})")

            # 在图像上绘制点和序号
            cv.circle(self.current_image, (x, y), 3, (0, 255, 0), -1)
            cv.putText(self.current_image, str(point_index), (x+5, y-5),
                      cv.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv.imshow("Image", self.current_image)
    
    def process_directory(self, directory_path):
        # 先加载已有的标注数据
        self.load_existing_points(directory_path)

        # 获取目录中所有jpg文件
        jpg_files = glob.glob(os.path.join(directory_path, "*.jpg"))
        jpg_files.extend(glob.glob(os.path.join(directory_path, "*.JPG")))

        if not jpg_files:
            print("目录中没有找到jpg文件")
            return

        print(f"找到 {len(jpg_files)} 个jpg文件")

        # 创建窗口并设置鼠标回调
        cv.namedWindow("Image", cv.WINDOW_NORMAL)
        cv.setMouseCallback("Image", self.mouse_callback)
        
        for img_path in jpg_files:
            filename = os.path.basename(img_path)
            print(f"\n处理图片: {filename}")

            # 读取图像
            img = cv.imread(img_path)
            if img is None:
                print(f"无法读取图片: {img_path}")
                continue

            # 重置当前图片的状态
            self.current_image = img.copy()
            self.current_filename = filename

            # 检查是否有已有的标注数据
            if filename in self.all_results:
                self.points = self.all_results[filename].copy()
                print(f"加载已有标注: {len(self.points)} 个点")
                print("在已有标注基础上继续标注，左键点击添加新点，SPACE键下一张，ESC键退出")

                # 在图像上绘制已有的点
                for i, (x, y) in enumerate(self.points):
                    cv.circle(self.current_image, (x, y), 5, (0, 255, 0), -1)
                    # 可选：在点旁边显示序号
                    cv.putText(self.current_image, str(i+1), (x+5, y-5),
                              cv.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            else:
                self.points = []
                print("新图片，左键点击收集坐标，SPACE键下一张，ESC键退出")

            # 显示图像
            cv.imshow("Image", self.current_image)
            
            # 等待用户操作
            while True:
                key = cv.waitKey(0) & 0xFF
                if key == 27:  # ESC键退出
                    self.save_results(directory_path)
                    cv.destroyAllWindows()
                    return
                elif key == 32:  # SPACE键下一张
                    break
            
            # 保存当前图片的点坐标
            if self.points:
                self.all_results[filename] = self.points.copy()
                print(f"收集到 {len(self.points)} 个点")
        
        # 保存所有结果
        self.save_results(directory_path)
        cv.destroyAllWindows()
    
    def save_results(self, directory_path):
        output_file = os.path.join(directory_path, "points_output.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, (filename, points) in enumerate(self.all_results.items()):
                if i > 0:
                    f.write("\n\n")
                
                # 格式化输出
                f.write(f'{{"{filename}", {{')
                
                point_strs = []
                for x, y in points:
                    point_strs.append(f"Point2f({x}, {y})")
                
                f.write(", ".join(point_strs))
                f.write("}}")
                
                if i < len(self.all_results) - 1:
                    f.write(",")
        
        print(f"\n结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='收集图像上的点击坐标')
    parser.add_argument('directory', help='包含jpg文件的目录路径')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.directory):
        print(f"错误: {args.directory} 不是有效的目录")
        sys.exit(1)
    
    collector = PointCollector()
    collector.process_directory(args.directory)

if __name__ == '__main__':
    print(__doc__)
    main()